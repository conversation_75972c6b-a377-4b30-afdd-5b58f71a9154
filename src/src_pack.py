import sys
from PyQt5.QtWidgets import (
    QWidget,QApplication,QAbstractItemView
)
from PyQt5 import (
    uic
)
from PyQt5.QtCore import (
    QStringListModel
)
from pathlib import Path
from typing import cast

def InfoWindow(msg:str = "helloworld"):
    # 使用现有的 QApplication 实例，不创建新的
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    window: QWidget = cast(QWidget, uic.loadUi(Path("src") / "infowindow.ui"))
    window.Info.setText(msg)
    window.show()
    return window  # 返回窗口对象而不是退出应用

def SettingWindow():
    # 使用现有的 QApplication 实例，不创建新的
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    window: QWidget = cast(QWidget, uic.loadUi(Path("src") / "settings.ui"))

    Mlist = ["关于"]
    model = QStringListModel()
    model.setStringList(Mlist)

    window.settings_list.setEditTriggers(QAbstractItemView.NoEditTriggers)
    window.settings_list.setModel(model)
    def clicked(index):
        item_index = index.row()
        if item_index == 0:
            InfoWindow("关于")
    window.settings_list.clicked.connect(clicked)
    window.show()
    return window  # 返回窗口对象而不是退出应用