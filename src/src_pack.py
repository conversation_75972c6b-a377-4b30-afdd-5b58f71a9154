import sys
from PyQt5.QtWidgets import (
    QWidget,QApplication
)
from PyQt5 import (
    uic
)
from PyQt5.QtCore import (
    QStringListModel
)
from pathlib import Path

def InfoWindow(msg:str = "helloworld"):
    app: QApplication = QApplication(sys.argv)
    window: QWidget = uic.loadUi(Path("src") / "infowindow.ui")
    window.Info.setText(msg)
    window.show()
    sys.exit(app.exec_())

def SettingWindow():
    app: QApplication = QApplication(sys.argv)
    window: QWidget = uic.loadUi(Path("src") / "settings.ui")

    model = QStringListModel()
    model.setStringList(["关于"])
    window.settings_list.setModel(model)
    window.settings_list.clicked.connect(
        lambda: InfoWindow("关于")
    )
    window.show()
    sys.exit(app.exec_())
