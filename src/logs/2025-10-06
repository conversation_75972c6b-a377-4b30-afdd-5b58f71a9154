2025-10-06 18:02:40	组件 centralwidget:
*--------------------------------------------------*
2025-10-06 18:02:40	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	---
*--------------------------------------------------*
2025-10-06 18:02:40	组件 background:
*--------------------------------------------------*
2025-10-06 18:02:40	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	---
*--------------------------------------------------*
2025-10-06 18:02:40	组件 sidebar:
*--------------------------------------------------*
2025-10-06 18:02:40	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:02:40	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	---
*--------------------------------------------------*
2025-10-06 18:02:40	组件 clock:
*--------------------------------------------------*
2025-10-06 18:02:40	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-06 18:02:40	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-06 18:02:40	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:02:40	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-06 18:02:40	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-06 18:02:40	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-06 18:02:40	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:02:40	---
*--------------------------------------------------*
2025-10-06 18:02:40	组件 exit:
*--------------------------------------------------*
2025-10-06 18:02:40	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-06 18:02:40	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:02:40	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:02:40	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:02:40	---
*--------------------------------------------------*
2025-10-06 18:02:40	组件 settings:
*--------------------------------------------------*
2025-10-06 18:02:40	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-06 18:02:40	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-06 18:02:40	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:02:40	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:02:40	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:02:40	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:02:40	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:02:40	---
*--------------------------------------------------*
2025-10-06 18:05:34	组件 centralwidget:
*--------------------------------------------------*
2025-10-06 18:05:34	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	---
*--------------------------------------------------*
2025-10-06 18:05:34	组件 background:
*--------------------------------------------------*
2025-10-06 18:05:34	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	---
*--------------------------------------------------*
2025-10-06 18:05:34	组件 sidebar:
*--------------------------------------------------*
2025-10-06 18:05:34	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:05:34	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	---
*--------------------------------------------------*
2025-10-06 18:05:34	组件 clock:
*--------------------------------------------------*
2025-10-06 18:05:34	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-06 18:05:34	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-06 18:05:34	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:05:34	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-06 18:05:34	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-06 18:05:34	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-06 18:05:34	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:05:34	---
*--------------------------------------------------*
2025-10-06 18:05:34	组件 exit:
*--------------------------------------------------*
2025-10-06 18:05:34	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-06 18:05:34	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:05:34	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:05:34	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:05:34	---
*--------------------------------------------------*
2025-10-06 18:05:34	组件 settings:
*--------------------------------------------------*
2025-10-06 18:05:34	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-06 18:05:34	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-06 18:05:34	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:05:34	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:05:34	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:05:34	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:05:34	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:05:34	---
*--------------------------------------------------*
2025-10-06 18:06:04	组件 centralwidget:
*--------------------------------------------------*
2025-10-06 18:06:04	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	---
*--------------------------------------------------*
2025-10-06 18:06:04	组件 background:
*--------------------------------------------------*
2025-10-06 18:06:04	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	---
*--------------------------------------------------*
2025-10-06 18:06:04	组件 sidebar:
*--------------------------------------------------*
2025-10-06 18:06:04	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:06:04	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	---
*--------------------------------------------------*
2025-10-06 18:06:04	组件 clock:
*--------------------------------------------------*
2025-10-06 18:06:04	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-06 18:06:04	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-06 18:06:04	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:06:04	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-06 18:06:04	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-06 18:06:04	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-06 18:06:04	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:06:04	---
*--------------------------------------------------*
2025-10-06 18:06:04	组件 exit:
*--------------------------------------------------*
2025-10-06 18:06:04	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-06 18:06:04	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:06:04	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:06:04	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:06:04	---
*--------------------------------------------------*
2025-10-06 18:06:04	组件 settings:
*--------------------------------------------------*
2025-10-06 18:06:04	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-06 18:06:04	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-06 18:06:04	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:06:04	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:06:04	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:06:04	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:06:04	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:06:04	---
*--------------------------------------------------*
2025-10-06 18:07:30	组件 centralwidget:
*--------------------------------------------------*
2025-10-06 18:07:30	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	---
*--------------------------------------------------*
2025-10-06 18:07:30	组件 background:
*--------------------------------------------------*
2025-10-06 18:07:30	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	---
*--------------------------------------------------*
2025-10-06 18:07:30	组件 sidebar:
*--------------------------------------------------*
2025-10-06 18:07:30	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:30	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	---
*--------------------------------------------------*
2025-10-06 18:07:30	组件 clock:
*--------------------------------------------------*
2025-10-06 18:07:30	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-06 18:07:30	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-06 18:07:30	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:07:30	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-06 18:07:30	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-06 18:07:30	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-06 18:07:30	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:07:30	---
*--------------------------------------------------*
2025-10-06 18:07:30	组件 exit:
*--------------------------------------------------*
2025-10-06 18:07:30	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-06 18:07:30	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:30	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:07:30	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:30	---
*--------------------------------------------------*
2025-10-06 18:07:30	组件 settings:
*--------------------------------------------------*
2025-10-06 18:07:30	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-06 18:07:30	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-06 18:07:30	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:30	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:07:30	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:30	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:07:30	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:30	---
*--------------------------------------------------*
2025-10-06 18:07:37	组件 centralwidget:
*--------------------------------------------------*
2025-10-06 18:07:37	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	---
*--------------------------------------------------*
2025-10-06 18:07:37	组件 background:
*--------------------------------------------------*
2025-10-06 18:07:37	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	---
*--------------------------------------------------*
2025-10-06 18:07:37	组件 sidebar:
*--------------------------------------------------*
2025-10-06 18:07:37	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-06 18:07:37	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	---
*--------------------------------------------------*
2025-10-06 18:07:37	组件 clock:
*--------------------------------------------------*
2025-10-06 18:07:37	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-06 18:07:37	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-06 18:07:37	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:07:37	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-06 18:07:37	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-06 18:07:37	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-06 18:07:37	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-06 18:07:37	---
*--------------------------------------------------*
2025-10-06 18:07:37	组件 exit:
*--------------------------------------------------*
2025-10-06 18:07:37	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-06 18:07:37	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:37	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-06 18:07:37	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:37	---
*--------------------------------------------------*
2025-10-06 18:07:37	组件 settings:
*--------------------------------------------------*
2025-10-06 18:07:37	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-06 18:07:37	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-06 18:07:37	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-06 18:07:37	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:07:37	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:37	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-06 18:07:37	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-06 18:07:37	---
*--------------------------------------------------*
