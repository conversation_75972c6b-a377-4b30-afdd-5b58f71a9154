2025-10-05 07:04:45	组件 centralwidget:
*--------------------------------------------------*
2025-10-05 07:04:45	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	---
*--------------------------------------------------*
2025-10-05 07:04:45	组件 background:
*--------------------------------------------------*
2025-10-05 07:04:45	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	---
*--------------------------------------------------*
2025-10-05 07:04:45	组件 sidebar:
*--------------------------------------------------*
2025-10-05 07:04:45	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:04:45	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	---
*--------------------------------------------------*
2025-10-05 07:04:45	组件 clock:
*--------------------------------------------------*
2025-10-05 07:04:45	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-05 07:04:45	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-05 07:04:45	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:04:45	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-05 07:04:45	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-05 07:04:45	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-05 07:04:45	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:04:45	---
*--------------------------------------------------*
2025-10-05 07:04:45	组件 exit:
*--------------------------------------------------*
2025-10-05 07:04:45	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-05 07:04:45	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:04:45	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:04:45	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:04:45	---
*--------------------------------------------------*
2025-10-05 07:04:45	组件 settings:
*--------------------------------------------------*
2025-10-05 07:04:45	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-05 07:04:45	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-05 07:04:45	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:04:45	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:04:45	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:04:45	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:04:45	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:04:45	---
*--------------------------------------------------*
2025-10-05 07:40:48	组件 centralwidget:
*--------------------------------------------------*
2025-10-05 07:40:48	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	---
*--------------------------------------------------*
2025-10-05 07:40:48	组件 background:
*--------------------------------------------------*
2025-10-05 07:40:48	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	---
*--------------------------------------------------*
2025-10-05 07:40:48	组件 sidebar:
*--------------------------------------------------*
2025-10-05 07:40:48	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:40:48	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	---
*--------------------------------------------------*
2025-10-05 07:40:48	组件 clock:
*--------------------------------------------------*
2025-10-05 07:40:48	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-05 07:40:48	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-05 07:40:48	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:40:48	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-05 07:40:48	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-05 07:40:48	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-05 07:40:48	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:40:48	---
*--------------------------------------------------*
2025-10-05 07:40:48	组件 exit:
*--------------------------------------------------*
2025-10-05 07:40:48	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-05 07:40:48	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:40:48	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:40:48	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:40:48	---
*--------------------------------------------------*
2025-10-05 07:40:48	组件 settings:
*--------------------------------------------------*
2025-10-05 07:40:48	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-05 07:40:48	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-05 07:40:48	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:40:48	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:40:48	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:40:48	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:40:48	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:40:48	---
*--------------------------------------------------*
2025-10-05 07:43:18	组件 centralwidget:
*--------------------------------------------------*
2025-10-05 07:43:18	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	---
*--------------------------------------------------*
2025-10-05 07:43:18	组件 background:
*--------------------------------------------------*
2025-10-05 07:43:18	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	---
*--------------------------------------------------*
2025-10-05 07:43:18	组件 sidebar:
*--------------------------------------------------*
2025-10-05 07:43:18	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:43:18	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	---
*--------------------------------------------------*
2025-10-05 07:43:18	组件 clock:
*--------------------------------------------------*
2025-10-05 07:43:18	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-05 07:43:18	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-05 07:43:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:43:18	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-05 07:43:18	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-05 07:43:18	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-05 07:43:18	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:43:18	---
*--------------------------------------------------*
2025-10-05 07:43:18	组件 exit:
*--------------------------------------------------*
2025-10-05 07:43:18	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-05 07:43:18	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:43:18	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:43:18	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:43:18	---
*--------------------------------------------------*
2025-10-05 07:43:18	组件 settings:
*--------------------------------------------------*
2025-10-05 07:43:18	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-05 07:43:18	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-05 07:43:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:43:18	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:43:18	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:43:18	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:43:18	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:43:18	---
*--------------------------------------------------*
2025-10-05 07:47:11	组件 centralwidget:
*--------------------------------------------------*
2025-10-05 07:47:11	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	---
*--------------------------------------------------*
2025-10-05 07:47:11	组件 background:
*--------------------------------------------------*
2025-10-05 07:47:11	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	---
*--------------------------------------------------*
2025-10-05 07:47:11	组件 sidebar:
*--------------------------------------------------*
2025-10-05 07:47:11	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:11	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	---
*--------------------------------------------------*
2025-10-05 07:47:11	组件 clock:
*--------------------------------------------------*
2025-10-05 07:47:11	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-05 07:47:11	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-05 07:47:11	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:47:11	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-05 07:47:11	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-05 07:47:11	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-05 07:47:11	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:47:11	---
*--------------------------------------------------*
2025-10-05 07:47:11	组件 exit:
*--------------------------------------------------*
2025-10-05 07:47:11	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-05 07:47:11	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:11	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:47:11	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:11	---
*--------------------------------------------------*
2025-10-05 07:47:11	组件 settings:
*--------------------------------------------------*
2025-10-05 07:47:11	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-05 07:47:11	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-05 07:47:11	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:11	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:47:11	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:11	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:47:11	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:11	---
*--------------------------------------------------*
2025-10-05 07:47:15	打开设置窗口时出错: setEditTriggers(self, triggers: Union[QAbstractItemView.EditTriggers, QAbstractItemView.EditTrigger]): not enough arguments
*--------------------------------------------------*
2025-10-05 07:47:18	打开设置窗口时出错: setEditTriggers(self, triggers: Union[QAbstractItemView.EditTriggers, QAbstractItemView.EditTrigger]): not enough arguments
*--------------------------------------------------*
2025-10-05 07:47:22	打开设置窗口时出错: setEditTriggers(self, triggers: Union[QAbstractItemView.EditTriggers, QAbstractItemView.EditTrigger]): not enough arguments
*--------------------------------------------------*
2025-10-05 07:47:59	组件 centralwidget:
*--------------------------------------------------*
2025-10-05 07:47:59	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	---
*--------------------------------------------------*
2025-10-05 07:47:59	组件 background:
*--------------------------------------------------*
2025-10-05 07:47:59	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	---
*--------------------------------------------------*
2025-10-05 07:47:59	组件 sidebar:
*--------------------------------------------------*
2025-10-05 07:47:59	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-05 07:47:59	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	---
*--------------------------------------------------*
2025-10-05 07:47:59	组件 clock:
*--------------------------------------------------*
2025-10-05 07:47:59	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-05 07:47:59	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-05 07:47:59	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:47:59	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-05 07:47:59	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-05 07:47:59	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-05 07:47:59	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-05 07:47:59	---
*--------------------------------------------------*
2025-10-05 07:47:59	组件 exit:
*--------------------------------------------------*
2025-10-05 07:47:59	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-05 07:47:59	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:59	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-05 07:47:59	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:59	---
*--------------------------------------------------*
2025-10-05 07:47:59	组件 settings:
*--------------------------------------------------*
2025-10-05 07:47:59	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-05 07:47:59	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-05 07:47:59	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-05 07:47:59	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:47:59	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:59	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-05 07:47:59	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-05 07:47:59	---
*--------------------------------------------------*
