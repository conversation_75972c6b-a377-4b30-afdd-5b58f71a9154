2025-10-01 11:29:32	组件 centralwidget:
*----------*
2025-10-01 11:29:32	  原始位置: (0, 0)
*----------*
2025-10-01 11:29:32	  原始大小: 800 x 600
*----------*
2025-10-01 11:29:32	  百分比 - 大小: 100.0% x 100.0%
*----------*
2025-10-01 11:29:32	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:29:32	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  使用屏幕尺寸作为参考: 2880x1920
*----------*
2025-10-01 11:29:32	  计算后位置: (0, 0)
*----------*
2025-10-01 11:29:32	  计算后大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  最终位置: (0, 0)
*----------*
2025-10-01 11:29:32	  最终大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	---
*----------*
2025-10-01 11:29:32	组件 background:
*----------*
2025-10-01 11:29:32	  原始位置: (0, 0)
*----------*
2025-10-01 11:29:32	  原始大小: 800 x 600
*----------*
2025-10-01 11:29:32	  百分比 - 大小: 100.0% x 100.0%
*----------*
2025-10-01 11:29:32	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:29:32	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  使用父组件 'centralwidget' 作为参考: 2880x1920
*----------*
2025-10-01 11:29:32	  计算后位置: (0, 0)
*----------*
2025-10-01 11:29:32	  计算后大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  最终位置: (0, 0)
*----------*
2025-10-01 11:29:32	  最终大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	---
*----------*
2025-10-01 11:29:32	组件 sidebar:
*----------*
2025-10-01 11:29:32	  原始位置: (0, 0)
*----------*
2025-10-01 11:29:32	  原始大小: 130 x 600
*----------*
2025-10-01 11:29:32	  百分比 - 大小: 0.0% x 100.0%
*----------*
2025-10-01 11:29:32	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:29:32	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  使用父组件 'centralwidget' 作为参考: 2880x1920
*----------*
2025-10-01 11:29:32	  计算后位置: (0, 0)
*----------*
2025-10-01 11:29:32	  计算后大小: 130 x 1920
*----------*
2025-10-01 11:29:32	  最终位置: (0, 0)
*----------*
2025-10-01 11:29:32	  最终大小: 130 x 1920
*----------*
2025-10-01 11:29:32	---
*----------*
2025-10-01 11:29:32	组件 clock:
*----------*
2025-10-01 11:29:32	  原始位置: (15, 15)
*----------*
2025-10-01 11:29:32	  原始大小: 100 x 30
*----------*
2025-10-01 11:29:32	  百分比 - 大小: 12.5% x 5.0%
*----------*
2025-10-01 11:29:32	  百分比 - 位置: 1.875% x 2.5%
*----------*
2025-10-01 11:29:32	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  使用父组件 'sidebar' 作为参考: 130x1920
*----------*
2025-10-01 11:29:32	  计算后位置: (1, 38)
*----------*
2025-10-01 11:29:32	  计算后大小: 345 x 96
*----------*
2025-10-01 11:29:32	  位置已调整: (1, 38) → (0, 38)
*----------*
2025-10-01 11:29:32	  调整原因: 确保组件完全显示在屏幕内
*----------*
2025-10-01 11:29:32	  最终位置: (0, 38)
*----------*
2025-10-01 11:29:32	  最终大小: 345 x 96
*----------*
2025-10-01 11:29:32	---
*----------*
2025-10-01 11:29:32	组件 exit:
*----------*
2025-10-01 11:29:32	  原始位置: (100, 570)
*----------*
2025-10-01 11:29:32	  原始大小: 20 x 20
*----------*
2025-10-01 11:29:32	  百分比 - 大小: 0.0% x 0.0%
*----------*
2025-10-01 11:29:32	  百分比 - 位置: 77.0% x 95.0%
*----------*
2025-10-01 11:29:32	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  使用父组件 'sidebar' 作为参考: 130x1920
*----------*
2025-10-01 11:29:32	  计算后位置: (100, 1824)
*----------*
2025-10-01 11:29:32	  计算后大小: 20 x 20
*----------*
2025-10-01 11:29:32	  最终位置: (100, 1824)
*----------*
2025-10-01 11:29:32	  最终大小: 20 x 20
*----------*
2025-10-01 11:29:32	---
*----------*
2025-10-01 11:29:32	组件 settings:
*----------*
2025-10-01 11:29:32	  原始位置: (75, 570)
*----------*
2025-10-01 11:29:32	  原始大小: 20 x 20
*----------*
2025-10-01 11:29:32	  百分比 - 大小: 0.0% x 0.0%
*----------*
2025-10-01 11:29:32	  百分比 - 位置: 57.99999999999999% x 95.0%
*----------*
2025-10-01 11:29:32	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:29:32	  使用父组件 'sidebar' 作为参考: 130x1920
*----------*
2025-10-01 11:29:32	  计算后位置: (74, 1824)
*----------*
2025-10-01 11:29:32	  计算后大小: 20 x 20
*----------*
2025-10-01 11:29:32	  最终位置: (74, 1824)
*----------*
2025-10-01 11:29:32	  最终大小: 20 x 20
*----------*
2025-10-01 11:29:32	---
*----------*
2025-10-01 11:47:21	组件 centralwidget:
*----------*
2025-10-01 11:47:21	  原始位置: (0, 0)
*----------*
2025-10-01 11:47:21	  原始大小: 800 x 600
*----------*
2025-10-01 11:47:21	  百分比 - 大小: 100.0% x 100.0%
*----------*
2025-10-01 11:47:21	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:47:21	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  使用屏幕尺寸作为参考: 2880x1920
*----------*
2025-10-01 11:47:21	  计算后位置: (0, 0)
*----------*
2025-10-01 11:47:21	  计算后大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  最终位置: (0, 0)
*----------*
2025-10-01 11:47:21	  最终大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	---
*----------*
2025-10-01 11:47:21	组件 background:
*----------*
2025-10-01 11:47:21	  原始位置: (0, 0)
*----------*
2025-10-01 11:47:21	  原始大小: 800 x 600
*----------*
2025-10-01 11:47:21	  百分比 - 大小: 100.0% x 100.0%
*----------*
2025-10-01 11:47:21	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:47:21	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  使用父组件 'centralwidget' 作为参考: 2880x1920
*----------*
2025-10-01 11:47:21	  计算后位置: (0, 0)
*----------*
2025-10-01 11:47:21	  计算后大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  最终位置: (0, 0)
*----------*
2025-10-01 11:47:21	  最终大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	---
*----------*
2025-10-01 11:47:21	组件 sidebar:
*----------*
2025-10-01 11:47:21	  原始位置: (0, 0)
*----------*
2025-10-01 11:47:21	  原始大小: 130 x 600
*----------*
2025-10-01 11:47:21	  百分比 - 大小: 0.0% x 100.0%
*----------*
2025-10-01 11:47:21	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:47:21	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  使用父组件 'centralwidget' 作为参考: 2880x1920
*----------*
2025-10-01 11:47:21	  计算后位置: (0, 0)
*----------*
2025-10-01 11:47:21	  计算后大小: 130 x 1920
*----------*
2025-10-01 11:47:21	  最终位置: (0, 0)
*----------*
2025-10-01 11:47:21	  最终大小: 130 x 1920
*----------*
2025-10-01 11:47:21	---
*----------*
2025-10-01 11:47:21	组件 clock:
*----------*
2025-10-01 11:47:21	  原始位置: (15, 15)
*----------*
2025-10-01 11:47:21	  原始大小: 100 x 30
*----------*
2025-10-01 11:47:21	  百分比 - 大小: 12.5% x 5.0%
*----------*
2025-10-01 11:47:21	  百分比 - 位置: 1.875% x 2.5%
*----------*
2025-10-01 11:47:21	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  使用父组件 'sidebar' 作为参考: 130x1920
*----------*
2025-10-01 11:47:21	  计算后位置: (1, 38)
*----------*
2025-10-01 11:47:21	  计算后大小: 345 x 96
*----------*
2025-10-01 11:47:21	  位置已调整: (1, 38) → (0, 38)
*----------*
2025-10-01 11:47:21	  调整原因: 确保组件完全显示在屏幕内
*----------*
2025-10-01 11:47:21	  最终位置: (0, 38)
*----------*
2025-10-01 11:47:21	  最终大小: 345 x 96
*----------*
2025-10-01 11:47:21	---
*----------*
2025-10-01 11:47:21	组件 exit:
*----------*
2025-10-01 11:47:21	  原始位置: (100, 570)
*----------*
2025-10-01 11:47:21	  原始大小: 20 x 20
*----------*
2025-10-01 11:47:21	  百分比 - 大小: 0.0% x 0.0%
*----------*
2025-10-01 11:47:21	  百分比 - 位置: 77.0% x 95.0%
*----------*
2025-10-01 11:47:21	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  使用父组件 'sidebar' 作为参考: 130x1920
*----------*
2025-10-01 11:47:21	  计算后位置: (100, 1824)
*----------*
2025-10-01 11:47:21	  计算后大小: 20 x 20
*----------*
2025-10-01 11:47:21	  最终位置: (100, 1824)
*----------*
2025-10-01 11:47:21	  最终大小: 20 x 20
*----------*
2025-10-01 11:47:21	---
*----------*
2025-10-01 11:47:21	组件 settings:
*----------*
2025-10-01 11:47:21	  原始位置: (75, 570)
*----------*
2025-10-01 11:47:21	  原始大小: 20 x 20
*----------*
2025-10-01 11:47:21	  百分比 - 大小: 0.0% x 0.0%
*----------*
2025-10-01 11:47:21	  百分比 - 位置: 57.99999999999999% x 95.0%
*----------*
2025-10-01 11:47:21	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:47:21	  使用父组件 'sidebar' 作为参考: 130x1920
*----------*
2025-10-01 11:47:21	  计算后位置: (74, 1824)
*----------*
2025-10-01 11:47:21	  计算后大小: 20 x 20
*----------*
2025-10-01 11:47:21	  最终位置: (74, 1824)
*----------*
2025-10-01 11:47:21	  最终大小: 20 x 20
*----------*
2025-10-01 11:47:21	---
*----------*
2025-10-01 11:56:55	组件 centralwidget:
*----------*
2025-10-01 11:56:55	  原始位置: (0, 0)
*----------*
2025-10-01 11:56:55	  原始大小: 800 x 600
*----------*
2025-10-01 11:56:55	  百分比 - 大小: 100.0% x 100.0%
*----------*
2025-10-01 11:56:55	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:56:55	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	  使用屏幕尺寸作为参考: 2880x1920
*----------*
2025-10-01 11:56:55	  计算后位置: (0, 0)
*----------*
2025-10-01 11:56:55	  计算后大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	警告: 组件 'centralwidget' 不存在
*----------*
2025-10-01 11:56:55	组件 background:
*----------*
2025-10-01 11:56:55	  原始位置: (0, 0)
*----------*
2025-10-01 11:56:55	  原始大小: 800 x 600
*----------*
2025-10-01 11:56:55	  百分比 - 大小: 100.0% x 100.0%
*----------*
2025-10-01 11:56:55	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:56:55	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	  使用父组件 'centralwidget' 作为参考: 800x600
*----------*
2025-10-01 11:56:55	  计算后位置: (0, 0)
*----------*
2025-10-01 11:56:55	  计算后大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	警告: 组件 'background' 不存在
*----------*
2025-10-01 11:56:55	组件 sidebar:
*----------*
2025-10-01 11:56:55	  原始位置: (0, 0)
*----------*
2025-10-01 11:56:55	  原始大小: 130 x 600
*----------*
2025-10-01 11:56:55	  百分比 - 大小: 0.0% x 100.0%
*----------*
2025-10-01 11:56:55	  百分比 - 位置: 0.0% x 0.0%
*----------*
2025-10-01 11:56:55	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	  使用父组件 'centralwidget' 作为参考: 800x600
*----------*
2025-10-01 11:56:55	  计算后位置: (0, 0)
*----------*
2025-10-01 11:56:55	  计算后大小: 130 x 1920
*----------*
2025-10-01 11:56:55	警告: 组件 'sidebar' 不存在
*----------*
2025-10-01 11:56:55	组件 clock:
*----------*
2025-10-01 11:56:55	  原始位置: (15, 15)
*----------*
2025-10-01 11:56:55	  原始大小: 100 x 30
*----------*
2025-10-01 11:56:55	  百分比 - 大小: 12.5% x 5.0%
*----------*
2025-10-01 11:56:55	  百分比 - 位置: 1.875% x 2.5%
*----------*
2025-10-01 11:56:55	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	  使用父组件 'sidebar' 作为参考: 130x600
*----------*
2025-10-01 11:56:55	  计算后位置: (1, 12)
*----------*
2025-10-01 11:56:55	  计算后大小: 345 x 96
*----------*
2025-10-01 11:56:55	警告: 组件 'clock' 不存在
*----------*
2025-10-01 11:56:55	组件 exit:
*----------*
2025-10-01 11:56:55	  原始位置: (100, 570)
*----------*
2025-10-01 11:56:55	  原始大小: 20 x 20
*----------*
2025-10-01 11:56:55	  百分比 - 大小: 0.0% x 0.0%
*----------*
2025-10-01 11:56:55	  百分比 - 位置: 77.0% x 95.0%
*----------*
2025-10-01 11:56:55	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	  使用父组件 'sidebar' 作为参考: 130x600
*----------*
2025-10-01 11:56:55	  计算后位置: (100, 570)
*----------*
2025-10-01 11:56:55	  计算后大小: 20 x 20
*----------*
2025-10-01 11:56:55	警告: 组件 'exit' 不存在
*----------*
2025-10-01 11:56:55	组件 settings:
*----------*
2025-10-01 11:56:55	  原始位置: (75, 570)
*----------*
2025-10-01 11:56:55	  原始大小: 20 x 20
*----------*
2025-10-01 11:56:55	  百分比 - 大小: 0.0% x 0.0%
*----------*
2025-10-01 11:56:55	  百分比 - 位置: 57.99999999999999% x 95.0%
*----------*
2025-10-01 11:56:55	  屏幕大小: 2880 x 1920
*----------*
2025-10-01 11:56:55	  使用父组件 'sidebar' 作为参考: 130x600
*----------*
2025-10-01 11:56:55	  计算后位置: (74, 570)
*----------*
2025-10-01 11:56:55	  计算后大小: 20 x 20
*----------*
2025-10-01 11:56:55	警告: 组件 'settings' 不存在
*----------*
2025-10-01 14:01:47	组件 centralwidget:
*--------------------------------------------------*
2025-10-01 14:01:47	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:01:47	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	警告: 组件 'centralwidget' 不存在
*--------------------------------------------------*
2025-10-01 14:01:47	组件 background:
*--------------------------------------------------*
2025-10-01 14:01:47	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:01:47	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	  使用父组件 'centralwidget' 作为参考: 800x600
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	警告: 组件 'background' 不存在
*--------------------------------------------------*
2025-10-01 14:01:47	组件 sidebar:
*--------------------------------------------------*
2025-10-01 14:01:47	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:01:47	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	  使用父组件 'centralwidget' 作为参考: 800x600
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	警告: 组件 'sidebar' 不存在
*--------------------------------------------------*
2025-10-01 14:01:47	组件 clock:
*--------------------------------------------------*
2025-10-01 14:01:47	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-01 14:01:47	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-01 14:01:47	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后位置: (1, 12)
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-01 14:01:47	警告: 组件 'clock' 不存在
*--------------------------------------------------*
2025-10-01 14:01:47	组件 exit:
*--------------------------------------------------*
2025-10-01 14:01:47	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:01:47	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:01:47	警告: 组件 'exit' 不存在
*--------------------------------------------------*
2025-10-01 14:01:47	组件 settings:
*--------------------------------------------------*
2025-10-01 14:01:47	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-01 14:01:47	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-01 14:01:47	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:01:47	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后位置: (74, 570)
*--------------------------------------------------*
2025-10-01 14:01:47	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:01:47	警告: 组件 'settings' 不存在
*--------------------------------------------------*
2025-10-01 14:03:49	组件 centralwidget:
*--------------------------------------------------*
2025-10-01 14:03:49	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:03:49	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	处理组件 'centralwidget' 时出错: 'Package' object has no attribute 'window'
*--------------------------------------------------*
2025-10-01 14:03:49	组件 background:
*--------------------------------------------------*
2025-10-01 14:03:49	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:03:49	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	  使用父组件 'centralwidget' 作为参考: 800x600
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	处理组件 'background' 时出错: 'Package' object has no attribute 'window'
*--------------------------------------------------*
2025-10-01 14:03:49	组件 sidebar:
*--------------------------------------------------*
2025-10-01 14:03:49	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:03:49	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	  使用父组件 'centralwidget' 作为参考: 800x600
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	处理组件 'sidebar' 时出错: 'Package' object has no attribute 'window'
*--------------------------------------------------*
2025-10-01 14:03:49	组件 clock:
*--------------------------------------------------*
2025-10-01 14:03:49	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-01 14:03:49	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-01 14:03:49	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后位置: (1, 12)
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-01 14:03:49	处理组件 'clock' 时出错: 'Package' object has no attribute 'window'
*--------------------------------------------------*
2025-10-01 14:03:49	组件 exit:
*--------------------------------------------------*
2025-10-01 14:03:49	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:03:49	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:03:49	处理组件 'exit' 时出错: 'Package' object has no attribute 'window'
*--------------------------------------------------*
2025-10-01 14:03:49	组件 settings:
*--------------------------------------------------*
2025-10-01 14:03:49	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-01 14:03:49	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-01 14:03:49	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:03:49	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后位置: (74, 570)
*--------------------------------------------------*
2025-10-01 14:03:49	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:03:49	处理组件 'settings' 时出错: 'Package' object has no attribute 'window'
*--------------------------------------------------*
2025-10-01 14:11:18	组件 centralwidget:
*--------------------------------------------------*
2025-10-01 14:11:18	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:18	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	处理组件 'centralwidget' 时出错: 'QtHome' object has no attribute 'pkg'
*--------------------------------------------------*
2025-10-01 14:11:18	组件 background:
*--------------------------------------------------*
2025-10-01 14:11:18	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:18	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	  使用父组件 'centralwidget' 作为参考: 800x600
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	处理组件 'background' 时出错: 'QtHome' object has no attribute 'pkg'
*--------------------------------------------------*
2025-10-01 14:11:18	组件 sidebar:
*--------------------------------------------------*
2025-10-01 14:11:18	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:18	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	  使用父组件 'centralwidget' 作为参考: 800x600
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	处理组件 'sidebar' 时出错: 'QtHome' object has no attribute 'pkg'
*--------------------------------------------------*
2025-10-01 14:11:18	组件 clock:
*--------------------------------------------------*
2025-10-01 14:11:18	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-01 14:11:18	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-01 14:11:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后位置: (1, 12)
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-01 14:11:18	处理组件 'clock' 时出错: 'QtHome' object has no attribute 'pkg'
*--------------------------------------------------*
2025-10-01 14:11:18	组件 exit:
*--------------------------------------------------*
2025-10-01 14:11:18	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:11:18	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:18	处理组件 'exit' 时出错: 'QtHome' object has no attribute 'pkg'
*--------------------------------------------------*
2025-10-01 14:11:18	组件 settings:
*--------------------------------------------------*
2025-10-01 14:11:18	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-01 14:11:18	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-01 14:11:18	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:18	  使用父组件 'sidebar' 作为参考: 130x600
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后位置: (74, 570)
*--------------------------------------------------*
2025-10-01 14:11:18	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:18	处理组件 'settings' 时出错: 'QtHome' object has no attribute 'pkg'
*--------------------------------------------------*
2025-10-01 14:11:43	组件 centralwidget:
*--------------------------------------------------*
2025-10-01 14:11:43	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  使用屏幕尺寸作为参考: 2880x1920
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	---
*--------------------------------------------------*
2025-10-01 14:11:43	组件 background:
*--------------------------------------------------*
2025-10-01 14:11:43	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  原始大小: 800 x 600
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 大小: 100.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  最终大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	---
*--------------------------------------------------*
2025-10-01 14:11:43	组件 sidebar:
*--------------------------------------------------*
2025-10-01 14:11:43	  原始位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  原始大小: 130 x 600
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 大小: 0.0% x 100.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 位置: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  使用父组件 'centralwidget' 作为参考: 2880x1920
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后大小: 130 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  最终位置: (0, 0)
*--------------------------------------------------*
2025-10-01 14:11:43	  最终大小: 130 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	---
*--------------------------------------------------*
2025-10-01 14:11:43	组件 clock:
*--------------------------------------------------*
2025-10-01 14:11:43	  原始位置: (15, 15)
*--------------------------------------------------*
2025-10-01 14:11:43	  原始大小: 100 x 30
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 大小: 12.5% x 5.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 位置: 1.875% x 2.5%
*--------------------------------------------------*
2025-10-01 14:11:43	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后位置: (1, 38)
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后大小: 345 x 96
*--------------------------------------------------*
2025-10-01 14:11:43	  位置已调整: (1, 38) → (0, 38)
*--------------------------------------------------*
2025-10-01 14:11:43	  调整原因: 确保组件完全显示在屏幕内
*--------------------------------------------------*
2025-10-01 14:11:43	  最终位置: (0, 38)
*--------------------------------------------------*
2025-10-01 14:11:43	  最终大小: 345 x 96
*--------------------------------------------------*
2025-10-01 14:11:43	---
*--------------------------------------------------*
2025-10-01 14:11:43	组件 exit:
*--------------------------------------------------*
2025-10-01 14:11:43	  原始位置: (100, 570)
*--------------------------------------------------*
2025-10-01 14:11:43	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 位置: 77.0% x 95.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后位置: (100, 1824)
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:43	  最终位置: (100, 1824)
*--------------------------------------------------*
2025-10-01 14:11:43	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:43	---
*--------------------------------------------------*
2025-10-01 14:11:43	组件 settings:
*--------------------------------------------------*
2025-10-01 14:11:43	  原始位置: (75, 570)
*--------------------------------------------------*
2025-10-01 14:11:43	  原始大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 大小: 0.0% x 0.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  百分比 - 位置: 57.99999999999999% x 95.0%
*--------------------------------------------------*
2025-10-01 14:11:43	  屏幕大小: 2880 x 1920
*--------------------------------------------------*
2025-10-01 14:11:43	  使用父组件 'sidebar' 作为参考: 130x1920
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后位置: (74, 1824)
*--------------------------------------------------*
2025-10-01 14:11:43	  计算后大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:43	  最终位置: (74, 1824)
*--------------------------------------------------*
2025-10-01 14:11:43	  最终大小: 20 x 20
*--------------------------------------------------*
2025-10-01 14:11:43	---
*--------------------------------------------------*
