from PyQt5.QtWidgets import QWidget
import time

def percentage(num: int,per: int):
    return num * (per * 0.01)

def str_children(widget: QWidget) -> list[str]:
    all_children = widget.findChildren(QWidget)  # 获取所有Widget类型的后代
    all_children_str = [
        f"{child.objectName() or '无名组件'}"
        for child in all_children
    ]
    return all_children_str

def now(mode: int=0) -> str:
    if mode == 0:
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    elif mode == 1:
        return time.strftime("%H:%M:%S", time.localtime())
    elif mode == 2:
        return time.strftime("%Y-%m-%d", time.localtime())
    else :
        return time.strftime("%H:%M:%S", time.localtime())

def _adjust_position_for_parent(self, widget, x, y, width, height):
    """
    调整组件位置，确保组件完全显示在其父组件内
    返回调整后的 (x, y) 坐标
    """
    parent_widget = widget.parent()

    if parent_widget and parent_widget != self.window:
        # 使用父组件的尺寸
        parent_geometry = parent_widget.geometry()
        container_width = parent_geometry.width()
        container_height = parent_geometry.height()
    else:
        # 使用屏幕尺寸
        container_width = self.size.width()
        container_height = self.size.height()

    # 调整 X 坐标
    if x + width > container_width:
        # 如果组件右边超出容器，将其左移
        x = container_width - width
    if x < 0:
        # 如果组件左边超出容器，将其右移
        x = 0

    # 调整 Y 坐标
    if y + height > container_height:
        # 如果组件底部超出容器，将其上移
        y = container_height - height
    if y < 0:
        # 如果组件顶部超出容器，将其下移
        y = 0

    return x, y

def _get_widget_point(self, widget, dimension):
    """
    获取组件的位置百分比属性
    dimension: 'x' 或 'y'
    返回百分比值，如果没有设置则返回0
    """
    try:
        # 获取UI文件中设置的Point属性
        point_property = widget.property("Point")

        if point_property is not None:
            # 根据dimension返回对应的百分比值
            if dimension == 'x':
                value = float(point_property.x())
            elif dimension == 'y':
                value = float(point_property.y())
            else:
                return 0.0

            # 如果值大于1，认为已经是百分比形式(如12表示12%)
            if value <= 1.0:
                return value * 100
            else:
                return value

        return 0.0  # 默认返回0，表示保持原像素位置

    except (ValueError, TypeError, AttributeError):
        return 0.0

def _get_widget_percentage(self, widget, dimension):
    """
    获取组件的尺寸百分比属性
    dimension: 'width' 或 'height'
    返回百分比值，如果没有设置则返回0
    """
    try:
        # 获取UI文件中设置的Percentage属性
        percentage_property = widget.property("Percentage")

        if percentage_property is not None:
            # 根据dimension返回对应的百分比值
            if dimension == 'width':
                value = float(percentage_property.width())
            elif dimension == 'height':
                value = float(percentage_property.height())
            else:
                return 0.0

            # 如果值小于1，认为是小数形式(如0.12表示12%)，需要乘以100
            # 如果值大于1，认为已经是百分比形式(如12表示12%)
            if value <= 1.0:
                return value * 100
            else:
                return value

        return 0.0  # 默认返回0，表示保持原像素大小

    except (ValueError, TypeError, AttributeError):
        return 0.0