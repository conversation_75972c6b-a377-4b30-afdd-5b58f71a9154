<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QFrame" name="sidebar">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>130</width>
      <height>600</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QFrame { background-color: rgba(255, 0, 0, 150); }</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <property name="Percentage" stdset="0">
     <sizef>
      <width>0.000000000000000</width>
      <height>1.000000000000000</height>
     </sizef>
    </property>
    <property name="Point" stdset="0">
     <pointf>
      <x>0.000000000000000</x>
      <y>0.000000000000000</y>
     </pointf>
    </property>
    <widget class="QLabel" name="clock">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>15</y>
       <width>100</width>
       <height>30</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel { background-color: transparent; color: black; }</string>
     </property>
     <property name="text">
      <string>19:30:12</string>
     </property>
     <property name="Percentage" stdset="0">
      <sizef>
       <width>0.125000000000000</width>
       <height>0.050000000000000</height>
      </sizef>
     </property>
     <property name="Point" stdset="0">
      <pointf>
       <x>0.018750000000000</x>
       <y>0.025000000000000</y>
      </pointf>
     </property>
    </widget>
    <widget class="QPushButton" name="exit">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>150</y>
       <width>20</width>
       <height>20</height>
      </rect>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>../pic/down.svg</normaloff>../pic/down.svg</iconset>
     </property>
     <property name="Percentage" stdset="0">
      <sizef>
       <width>0.000000000000000</width>
       <height>0.000000000000000</height>
      </sizef>
     </property>
     <property name="Point" stdset="0">
      <pointf>
       <x>0.120000000000000</x>
       <y>0.950000000000000</y>
      </pointf>
     </property>
    </widget>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>exit</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>110</x>
     <y>578</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>471</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
