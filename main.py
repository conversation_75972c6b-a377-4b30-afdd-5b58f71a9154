import sys
from src.src_math import (
    percentage
)
from PyQt5.QtWidgets import (
    QApplication,QMainWindow
)
from PyQt5 import (
    uic
)
from PyQt5.QtCore import (
    QTime,QTimer,Qt,qDebug
)

class QtHome:
    def __init__(self):
        app = QApplication(sys.argv)
        self.window: QMainWindow = uic.loadUi('src/main.ui')
        # 获取exit组件的引用
        self.exit = self.window.exit
        self.Init_window()
        self.Init_Clock()
        self.Init_Package()
        qDebug(str(self.exit.mapToGlobal(self.exit.pos())))
        sys.exit(app.exec_())
    
    def Init_window(self):
        ### 屏幕大小检测 ###
        screen = QApplication.primaryScreen()
        if screen:
            self.size = screen.geometry()
            if (self.size.width() < 800) and (self.size.height() < 600):
                raise SystemError("屏幕小,装不了,要想用,换电脑")
        
        del screen
        ### ----------- ###
        
        ### 初始化窗口 ###
        self.window.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.window.setWindowModality(Qt.WindowModality.WindowModal)
        self.window.showFullScreen()
        ### --------- ###

    def Init_Clock(self):
        self.window.clock.setText(QTime.currentTime().toString())
        self.Clock_T = QTimer()
        self.Clock_T.timeout.connect(self.Init_Clock)
        self.Clock_T.start(1000)

    def Init_Package(self):
        """
        根据UI组件的百分比属性设置组件大小和位置
        Percentage属性: 百分比为0时保持原像素级大小，否则按百分比计算
        Point属性: 百分比为0时保持原像素级位置，否则按百分比计算
        """
        # 获取所有需要处理的组件
        widgets_to_process = [
            'sidebar', 'clock', 'exit'  # 可以根据需要添加更多组件
        ]

        for widget_name in widgets_to_process:
            try:
                # 获取组件对象
                widget = getattr(self.window, widget_name)

                # 检查组件是否有百分比属性 (大小)
                width_percentage = self._get_widget_percentage(widget, 'width')
                height_percentage = self._get_widget_percentage(widget, 'height')

                # 检查组件是否有位置百分比属性
                x_percentage = self._get_widget_point(widget, 'x')
                y_percentage = self._get_widget_point(widget, 'y')

                # 获取组件当前的几何信息
                current_geometry = widget.geometry()

                # 调试信息
                print(f"组件 {widget_name}:")
                print(f"  原始位置: ({current_geometry.x()}, {current_geometry.y()})")
                print(f"  原始大小: {current_geometry.width()} x {current_geometry.height()}")
                print(f"  百分比 - 大小: {width_percentage}% x {height_percentage}%")
                print(f"  百分比 - 位置: {x_percentage}% x {y_percentage}%")
                print(f"  屏幕大小: {self.size.width()} x {self.size.height()}")

                # 计算新的宽度
                if width_percentage > 0:
                    new_width = int(percentage(self.size.width(), int(width_percentage)))
                else:
                    new_width = current_geometry.width()  # 保持原像素大小

                # 计算新的高度
                if height_percentage > 0:
                    new_height = int(percentage(self.size.height(), int(height_percentage)))
                else:
                    new_height = current_geometry.height()  # 保持原像素大小

                # 计算新的X位置
                if x_percentage > 0:
                    new_x = int(percentage(self.size.width(), int(x_percentage)))
                else:
                    new_x = current_geometry.x()  # 保持原像素位置

                # 计算新的Y位置
                if y_percentage > 0:
                    new_y = int(percentage(self.size.height(), int(y_percentage)))
                else:
                    new_y = current_geometry.y()  # 保持原像素位置

                print(f"  计算后位置: ({new_x}, {new_y})")
                print(f"  计算后大小: {new_width} x {new_height}")

                # 应用新的尺寸和位置
                widget.setGeometry(new_x, new_y, new_width, new_height)

                # 验证设置后的实际位置
                final_geometry = widget.geometry()
                print(f"  最终位置: ({final_geometry.x()}, {final_geometry.y()})")
                print(f"  最终大小: {final_geometry.width()} x {final_geometry.height()}")
                print("---")

            except AttributeError:
                # 如果组件不存在，跳过
                print(f"警告: 组件 '{widget_name}' 不存在")
                continue
            except Exception as e:
                print(f"处理组件 '{widget_name}' 时出错: {e}")
                continue

    def _get_widget_percentage(self, widget, dimension):
        """
        获取组件的尺寸百分比属性
        dimension: 'width' 或 'height'
        返回百分比值，如果没有设置则返回0
        """
        try:
            # 获取UI文件中设置的Percentage属性
            percentage_property = widget.property("Percentage")

            if percentage_property is not None:
                # 根据dimension返回对应的百分比值
                if dimension == 'width':
                    value = float(percentage_property.width())
                elif dimension == 'height':
                    value = float(percentage_property.height())
                else:
                    return 0.0

                # 如果值小于1，认为是小数形式(如0.12表示12%)，需要乘以100
                # 如果值大于1，认为已经是百分比形式(如12表示12%)
                if value <= 1.0:
                    return value * 100
                else:
                    return value

            return 0.0  # 默认返回0，表示保持原像素大小

        except (ValueError, TypeError, AttributeError):
            return 0.0

    def _get_widget_point(self, widget, dimension):
        """
        获取组件的位置百分比属性
        dimension: 'x' 或 'y'
        返回百分比值，如果没有设置则返回0
        """
        try:
            # 获取UI文件中设置的Point属性
            point_property = widget.property("Point")

            if point_property is not None:
                # 根据dimension返回对应的百分比值
                if dimension == 'x':
                    value = float(point_property.x())
                elif dimension == 'y':
                    value = float(point_property.y())
                else:
                    return 0.0

                # 如果值大于1，认为已经是百分比形式(如12表示12%)
                if value <= 1.0:
                    return value * 100
                else:
                    return value

            return 0.0  # 默认返回0，表示保持原像素位置

        except (ValueError, TypeError, AttributeError):
            return 0.0


if __name__ == '__main__':
    QtHome()