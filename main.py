import sys,json
from src.src_math import (
    percentage
)
from PyQt5.QtWidgets import (
    QApplication,QMainWindow
)
from PyQt5 import (
    uic
)
from PyQt5.QtCore import (
    QTime,QTimer,Qt
)

class QtHome:
    def __init__(self):
        app = QApplication(sys.argv)
        self.window: QMainWindow = uic.loadUi('src/main.ui')
        self.Init_window()
        self.Init_Clock()
        self.Init_Package()
        sys.exit(app.exec_())
    
    def Init_window(self):
        ### 屏幕大小检测 ###
        screen = QApplication.primaryScreen()
        if screen:
            self.size = screen.geometry()
            if (self.size.width() < 800) and (self.size.height() < 600):
                raise SystemError("屏幕小,装不了,要想用,换电脑")
        
        del screen
        ### ----------- ###
        
        ### 初始化窗口 ###
        self.window.setWindowFlags(self.window.windowFlags() | Qt.FramelessWindowHint)
        self.window.setWindowFlags(self.window.windowFlags() | Qt.WindowModal)
        self.window.showFullScreen()
        ### --------- ###

    def Init_Clock(self):
        self.window.clock.setText(QTime.currentTime().toString())
        self.Clock_T = QTimer()
        self.Clock_T.timeout.connect(self.Init_Clock)
        self.Clock_T.start(1000)

    def Init_Package(self):
        with open('src/package.json','r') as f:
            package: dict = json.load(f)

        ### 初始化检查列表,然后删除变量all ###
        all = package["package_list"]
        bool_a: dict = {}

        for then_K in range(len(all)):
            bool_a.update({
                all[then_K]: False
            })

        del all
        ### ---------------------------- ###

        then_P: list[str] = []
        then_L: dict = package
        path_stack: list[dict] = []
        running: bool = True
        while (running):
            ### 初始化变量
            """
                then_P (list): 当前组件
                then_L (dict): 当前目录
                then_K (str): 当前键
                init_P (dict): 记录当前属性同步情况（需初始化）
                init_U (dict): 记录当前属性单位（需初始化）
                re (dict_keys): 当前键目录的所有键（需初始化）
            """
            init_P: dict[str,bool] = {
                "width": False,
                "height": False
            }
            init_U: dict[str,str] = {
                "width": "%",
                "height": "%"
            }
            re = then_L.keys()

            ### 判断键类型，对号入座 ###
            # then_K代表当前遍历的键
            for then_K in re:
                if (then_K in ["width","height"]):
                    init_P[then_K] = True
                    if (then_P == ["MainWindow"]):
                        break
                    width_value = then_L.get(then_P[-1], 0)
                    current_widget = self.window
                    for widget_name in then_P:
                        current_widget = getattr(current_widget, widget_name)
                    if then_L["unit"] == "%":
                        width_value = percentage(
                            getattr(self.size,then_K),
                            then_L[then_K]
                        )
                    else:
                        width_value = then_L[then_K]
                    current_widget.setFixedWidth(int(width_value))
                    break

                if (then_K == "package"):
                    if path_stack:
                        then_L = path_stack.pop()
                        if then_P:
                            then_P.pop()
                    break

                try:
                    if (then_K in bool_a and not bool_a[tuple(then_P[-1])]):
                        path_stack.append(then_L)
                        then_P.append(then_K)
                        then_L = then_L[then_K]
                        bool_a[then_K] = True
                        break
                
                except KeyError:
                    pass
            ### -------------------- ###

            running = any(not v for v in bool_a.values())
                
            
            
            
if __name__ == '__main__':
    QtHome()