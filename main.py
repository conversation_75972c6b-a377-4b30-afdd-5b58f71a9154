import sys
from src import *
from PyQt5.QtWidgets import (
    QApplication,QMainWindow,QWidget,
    QPushButton,QLabel
)
from PyQt5 import (
    uic
)
from PyQt5.QtCore import (
    QTime,QTimer,Qt,qDebug
)
from typing import cast

class QtHome:
    def __init__(self):
        app = QApplication(sys.argv)
        self.window: QMainWindow = cast(QMainWindow, uic.loadUi('src/main.ui'))
        # 获取exit组件的引用
        self.exit = cast(QPushButton, self.window.exit)
        self.init_window()
        self.init_clock()
        self.init_package()
        qDebug(str(self.exit.mapToGlobal(self.exit.pos())))
        sys.exit(app.exec_())
    
    def init_window(self):
        ### 屏幕大小检测 ###
        screen = QApplication.primaryScreen()
        if screen:
            self.size = screen.geometry()
            if (self.size.width() < 800) and (self.size.height() < 600):
                raise SystemError("屏幕小,装不了,要想用,换电脑")
        
        del screen
        ### ----------- ###
        
        ### 初始化窗口 ###
        self.window.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.window.setWindowModality(Qt.WindowModality.WindowModal)
        self.window.showFullScreen()
        ### --------- ###

    def init_clock(self):
        """初始化时钟"""
        self.window.clock.setText(QTime.currentTime().toString())
        self.Clock_T = QTimer()
        self.Clock_T.timeout.connect(self.init_clock)
        self.Clock_T.start(1000)

    def init_package(self):
        """
        根据UI组件的百分比属性设置组件大小和位置
        Percentage属性: 百分比为0时保持原像素级大小，否则按百分比计算
        Point属性: 百分比为0时保持原像素级位置，否则按百分比计算
        """
        # 获取所有需要处理的组件
        widgets_to_process: list[str] = str_children(self.window)

        for widget_name in widgets_to_process:
            try:
                # 获取组件对象
                widget: QWidget = getattr(self.window, widget_name)

                # 检查组件是否有百分比属性 (大小)
                width_percentage = _get_widget_percentage(self, widget, 'width')
                height_percentage = _get_widget_percentage(self, widget, 'height')

                # 检查组件是否有位置百分比属性
                x_percentage = _get_widget_point(self, widget, 'x')
                y_percentage = _get_widget_point(self, widget, 'y')

                # 获取组件当前的几何信息
                current_geometry = widget.geometry()

                # 调试信息
                printl(f"组件 {widget_name}:")
                printl(f"  原始位置: ({current_geometry.x()}, {current_geometry.y()})")
                printl(f"  原始大小: {current_geometry.width()} x {current_geometry.height()}")
                printl(f"  百分比 - 大小: {width_percentage}% x {height_percentage}%")
                printl(f"  百分比 - 位置: {x_percentage}% x {y_percentage}%")
                printl(f"  屏幕大小: {self.size.width()} x {self.size.height()}")

                # 计算新的宽度
                if width_percentage > 0:
                    new_width = int(percentage(self.size.width(), int(width_percentage)))
                else:
                    new_width = current_geometry.width()  # 保持原像素大小

                # 计算新的高度
                if height_percentage > 0:
                    new_height = int(percentage(self.size.height(), int(height_percentage)))
                else:
                    new_height = current_geometry.height()  # 保持原像素大小

                # 获取父组件的尺寸作为参考
                parent_widget = widget.parent()
                if parent_widget and parent_widget != self.window:
                    # 如果有父组件且不是主窗口，使用父组件的尺寸
                    parent_geometry = parent_widget.geometry()
                    reference_width = parent_geometry.width()
                    reference_height = parent_geometry.height()
                    printl(f"  使用父组件 '{parent_widget.objectName()}' 作为参考: {reference_width}x{reference_height}")
                else:
                    # 否则使用屏幕尺寸
                    reference_width = self.size.width()
                    reference_height = self.size.height()
                    printl(f"  使用屏幕尺寸作为参考: {reference_width}x{reference_height}")

                # 计算新的X位置
                if x_percentage > 0:
                    new_x = int(percentage(reference_width, int(x_percentage)))
                else:
                    new_x = current_geometry.x()  # 保持原像素位置

                # 计算新的Y位置
                if y_percentage > 0:
                    new_y = int(percentage(reference_height, int(y_percentage)))
                else:
                    new_y = current_geometry.y()  # 保持原像素位置

                printl(f"  计算后位置: ({new_x}, {new_y})")
                printl(f"  计算后大小: {new_width} x {new_height}")

                # 检查并调整位置，确保组件完全显示在父组件内
                adjusted_x, adjusted_y = _adjust_position_for_parent(
                    self, widget, new_x, new_y, new_width, new_height
                )

                if adjusted_x != new_x or adjusted_y != new_y:
                    printl(f"  位置已调整: ({new_x}, {new_y}) → ({adjusted_x}, {adjusted_y})")
                    printl(f"  调整原因: 确保组件完全显示在屏幕内")

                # 应用新的尺寸和位置
                widget.setGeometry(adjusted_x, adjusted_y, new_width, new_height)

                # 验证设置后的实际位置
                final_geometry = widget.geometry()
                printl(f"  最终位置: ({final_geometry.x()}, {final_geometry.y()})")
                printl(f"  最终大小: {final_geometry.width()} x {final_geometry.height()}")
                printl("---")

            except AttributeError:
                # 如果组件不存在，跳过
                printl(f"警告: 组件 '{widget_name}' 不存在")
                continue
            except Exception as e:
                printl(f"处理组件 '{widget_name}' 时出错: {e}")
                continue
        # 存储设置窗口的引用，防止被垃圾回收
        self.setting_window = None

        # 检查是否存在 settings 按钮
        if hasattr(self.window, 'settings'):
            self.window.settings.clicked.connect(self.open_setting_window)
        if hasattr(self.window, 'exit'):
            self.window.exit.clicked.connect(quit)

    def open_setting_window(self):
        """打开设置窗口"""
        try:
            self.setting_window = SettingWindow()
        except Exception as e:
            printl(f"打开设置窗口时出错: {e}")

if __name__ == '__main__':
    QtHome()